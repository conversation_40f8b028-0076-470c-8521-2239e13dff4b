module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'plugin:react/recommended',
    'plugin:prettier/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react/jsx-runtime',
    'plugin:@tanstack/eslint-plugin-query/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:storybook/recommended'],
  ignorePatterns: ['dist', '.eslintrc.cjs', '*Orion.CORE*'],
  plugins: ['react-refresh', 'react', '@tanstack/query', 'import', '@typescript-eslint', "@stylistic/js"],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: "module",
  },
  settings: {
    react: {
      version: "detect",
    },
  },
  rules: {
    '@typescript-eslint/no-explicit-any': 'warn',
    'no-console': 'warn',
    'react/prop-types': 'off',
    'react/no-unescaped-entities': 'off',
    'prettier/prettier': [
      'error',
      {
        "printWidth": 100,
        "trailingComma": "all",
        "singleQuote": false,
        "tabWidth": 2,
        "useTabs": false,
        "semi": true,
        "bracketSpacing": true,
        "arrowParens": "always",
        "endOfLine": "auto",
      },
    ],
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    'semi': ['error', 'always'],
    '@typescript-eslint/no-unused-vars': [
      'warn',
      {
        args: 'after-used',
        ignoreRestSiblings: false,
        argsIgnorePattern: '^_.*?$',
      },
    ],
    'import/order': [
      'warn',
      {
        pathGroups: [
          {
            pattern: '~/**',
            group: 'external',
            position: 'after',
          },
          {
            pattern: '@/Orion.*/**',
            group: 'external',
            position: 'after',
          },
          {
            pattern: '@/**',
            group: 'parent',
            position: 'before',
          },
        ],
        'newlines-between': "always",
      },
    ],
    'react/self-closing-comp': [
      'error',
      {
        component: true,
        html: true,
      },
    ],
    'react/jsx-sort-props': [
      'warn',
      {
        callbacksLast: true,
        shorthandFirst: true,
        noSortAlphabetically: false,
        reservedFirst: true,
      },
    ],
    '@stylistic/js/indent': ['error', 2],
    '@tanstack/query/exhaustive-deps': 'error',
  },
}
