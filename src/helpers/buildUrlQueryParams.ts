const buildUrlQueryParams = (baseUrl: string, queryParams: object) => {
  const urlParams = new URLSearchParams();

  Object.entries(queryParams).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      if (value.length < 1) return;
      const stringifyValue = `[${value.join(",")}]`;
      urlParams.append(key, stringifyValue);
      return;
    }
    if (!!value) {
      urlParams.append(key, value);
    }
  });

  const queryString = urlParams.toString().replaceAll(/\+/g, encodeURIComponent(" "));
  return `${baseUrl}${queryString}`;
};

export default buildUrlQueryParams;
