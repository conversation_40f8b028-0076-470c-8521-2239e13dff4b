import { endpointsVersions } from "./index.ts";
import { EMVARIAS_COMPANY_ID } from "./companyIds.ts";

const isLocalhost = () => {
  return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
};

const buildPath = (path: string) => {
  if (isLocalhost()) {
    const cleanPath = path.replace(/^api\//, '');
    return cleanPath.startsWith('/') ? cleanPath : `/${cleanPath}`;
  }

  return path.startsWith('/') ? path : `/${path}`;
};

export const endpointsPathsBussiness = {
  USER_PARAMETERS: `api/authorization`,
  MASS_BALANCE: `/${endpointsVersions.MERCURY_REPORTS}/Reports/MassBalance`,
  GENERATE_DISCOUNT_REPORT: `/${endpointsVersions.MERCURY_REPORTS}/Reports/DiscountsReport`,
  REPORTS_PREVIEW: `/${endpointsVersions.MERCURY_REPORTS}/Reports/Preview`,
  RECYCLING_AREA: `/${endpointsVersions.MERCURY_REPORTS}/RecyclingArea`,
  REPORTS_EXPORT: `/${endpointsVersions.MERCURY_REPORTS}/Reports/Export`,
  WEIGHINS: `/${endpointsVersions.MERCURY_REPORTS}/Weighins`,
  VEHICLES: `api/core/vehicles?companyId=${EMVARIAS_COMPANY_ID}`,
  SERVICE_TYPE: `api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneServiceTypes`,
  NON_COMPLIANCE_MOTIVES: `api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneConfig/nonComplianceMotives`,
  ROUTE: `api/v2/companies/${EMVARIAS_COMPANY_ID}/hygieneRoutes`,
  REFRESH_TOKEN: `/api/authentication/refresh`,
  NIT: `/${endpointsVersions.MERCURY_REPORTS}/Clients`,
  GENERATE_SUI: `/${endpointsVersions.MERCURY_REPORTS}/Reports/ExportCompressedReports`,
  REGULATORY_ROUTES: `/${endpointsVersions.MERCURY_REPORTS}/Microroute/regulatory-routes`,
};
