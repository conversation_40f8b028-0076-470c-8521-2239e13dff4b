import { useQuery, UseQueryResult } from "@tanstack/react-query";

import { axiosInstanceUrbetrack } from "@/service/Axios";
import { endpointsPathsBussiness } from "@/constants";
import { AuthKeys } from "@/constants/authKeys.ts";

export interface UserParamsResponse {
  "mercury.show_all_menus": boolean;
}

export type UserParamsT = {
  ShowAllMenus: boolean;
};

const getUserParams = async () => {
  const response = await axiosInstanceUrbetrack.get<UserParamsResponse>(
    `${endpointsPathsBussiness.USER_PARAMETERS}/mercury.show_all_menus`,
  );
  return response.data;
};

const useGetUserParameters = (): UseQueryResult<UserParamsT | undefined> => {
  const token = localStorage.getItem(AuthKeys.accessTokenLegacy);

  return useQuery({
    queryFn: getUserParams,
    queryKey: ["userParams", token],
    refetchOnMount: true,
    select: (data: UserParamsResponse | undefined) => {
      if (!data) {
        return undefined;
      }

      return {
        ShowAllMenus: data["mercury.show_all_menus"],
      } as UserParamsT;
    },
    meta: {
      errorMessage:
        "Ocurrió un error en la carga de los parámetros del usuario, intente refrescando nuevamente",
      toastId: "userParams",
    },
  });
};

export default useGetUserParameters;
