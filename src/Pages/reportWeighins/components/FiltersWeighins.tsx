import { useFiltersStore } from '@/Orion.CORE.Frontend/store';
import { FilterWeighinsE, nameModule } from '@/Pages/reportWeighins/helpers';
import { AnchorT } from '@/Orion.CORE.Frontend/models';
import { useUrlReportWeighinsStore } from '@/Pages/reportWeighins/store/useReportWeighinsStore.ts';
import dayjs from 'dayjs';
import { WeighinsReportFiltersT } from '@/Pages/reportWeighins/models';
import buildUrlQueryParams from '@/helpers/buildUrlQueryParams.ts';
import { endpointsPathsBussiness } from '@/constants';
import { Box, Grid, List, Input } from '@mui/material';
import { DrawerActionsFilters, DrawerTitle } from '@/Orion.CORE.Frontend/components';
import { DateRangeFilter } from '@/components';
import NITFilter from '../filters/NITFilter';
import LicensePlateFilter from '../filters/LicensePlateFilter';

type PropsT = {
  anchor?: AnchorT;
  title: string;
};

export default function FiltersWeighins({ anchor = 'right', title }: PropsT) {
  const startDate = useFiltersStore((state) => state.data[FilterWeighinsE.startDate]);
  const endDate = useFiltersStore((state) => state.data[FilterWeighinsE.endDate]);
  const licencePlate = useFiltersStore((state) => state.data[FilterWeighinsE.licensePlate]);
  const nit = useFiltersStore((state) => state.data[FilterWeighinsE.NIT]);
  const reportUrlStore = useUrlReportWeighinsStore((state) => state);
  const setHandleOpenDrawer = useFiltersStore((state) => state.setHandleOpenDrawer);
  const setApplyFilters = useFiltersStore((state) => state.setApplyFilters);
  const setData = useFiltersStore((state) => state.setData);

  const handleClearFilters = () => {
    setData(FilterWeighinsE.startDate, { selectedDate: dayjs().add(-1, 'year'), minDate: null, maxDate: dayjs() });
    setData(FilterWeighinsE.startDate, { selectedDate: dayjs().add(-1, 'minute'), minDate: dayjs().add(-1, 'year'), maxDate: dayjs() });
  };

  const handleApplyFilters = () => {
    setApplyFilters(nameModule, anchor);

    const weighinsReportFilters: WeighinsReportFiltersT = {
      fromDate: dayjs(startDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      toDate: dayjs(endDate.selectedDate)?.format('YYYY-MM-DD HH:MM:ss') || '',
      licensePlate: licencePlate?.selectedRadioButton?.value,
      nit: nit?.selectedRadioButton?.value,
    };

    const url = buildUrlQueryParams(`${endpointsPathsBussiness.WEIGHINS}?`, weighinsReportFilters);
    reportUrlStore.setUrlStore(url);
  };

  return (
    <Box
      className="filters-content"
      sx={{ width: anchor === 'top' || anchor === 'bottom' ? 'auto' : '320px', maxHeight: '100%' }}
      height="100%"
      role="presentation"
    >
      <Grid container height="100%" alignContent="space-between">
        <Grid>
          <DrawerTitle nameModule={nameModule} title={title} handleOpenDrawer={setHandleOpenDrawer} />
          <List disablePadding>
            <DateRangeFilter
              nameModule={nameModule}
              startDateFilterKey={FilterWeighinsE.startDate}
              startDateLabel={'Fecha de inicio'}
              endDateFilterKey={FilterWeighinsE.endDate}
              endDateLabel={'Fecha de fin'}
            />
            <NITFilter nameModule={nameModule} nitFilterKey={FilterWeighinsE.NIT} />
            <LicensePlateFilter nameModule={nameModule} licensePlateFilterKey={FilterWeighinsE.licensePlate} />
          </List>
        </Grid>
        <Grid container>
          <DrawerActionsFilters handleClearFilters={handleClearFilters} handleApplyFilters={handleApplyFilters} />
        </Grid>
      </Grid>
    </Box>
  );
}
