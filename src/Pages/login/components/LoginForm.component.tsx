import { FormEvent, useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { Box, Button, CircularProgress } from "@mui/material";

import { InputPassword, InputText } from "@/Orion.CORE.Frontend/components";

import logo from "../../../assets/login.png";
import { useLoginUser } from "../hooks/postLogin";

export const LoginForm = () => {
  const [user, setUser] = useState("");
  const [password, setPassword] = useState("");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isPasswordError, setPasswordError] = useState(false);
  const [isUserError, setUserError] = useState(false);

  const { mutate, isLoading, isError, data } = useLoginUser();

  useEffect(() => {
    if (data) {
      setIsAuthenticated(true);
    }
  }, [data?.token]);

  const handleLoginUser = async (event: FormEvent<HTMLFormElement> | null) => {
    event?.preventDefault();

    if (user == "" || password == "") {
      return;
    }

    mutate({ user, password });
  };

  const handleBlur = () => {
    if (!user) setUserError(true);
    else setUserError(false);
    if (!password) setPasswordError(true);
    else setPasswordError(false);
  };

  if (isAuthenticated) return <Navigate to={"/modules"} />;

  return (
    <div className=" flex h-screen w-full flex-row items-center justify-center">
      <form
        className="flex h-4/6 min-h-[500px] w-1/4 min-w-max flex-col rounded bg-white p-8 shadow-[5px_5px_15px_5px_rgba(0,0,0,0.05)]"
        onSubmit={handleLoginUser}
      >
        <header>
          <img alt="" className="h-32 w-full object-contain " src={logo} />
        </header>
        <fieldset className="h-full">
          <legend className="text-center pt-10">Inicio de sesión</legend>
          <section className="flex h-full flex-col justify-evenly">
            <InputText
              edit
              handleBlur={handleBlur}
              handleChange={(e: { target: HTMLInputElement }) => setUser(e.target?.value ?? "")}
              label="Nombre de usuario"
              messageError={isUserError ? "Ingrese un nombre de usuario válido" : ""}
              name="user"
              value={user}
            />

            <InputPassword
              handleBlur={handleBlur}
              handleChange={(e: { target: HTMLInputElement }) => setPassword(e.target?.value ?? "")}
              label="Contraseña"
              messageError={isPasswordError ? "Ingrese una contraseña válida" : ""}
              name="password"
              value={password}
            />
            <footer className="w-full text-center">
              <Button type="submit" onClick={() => handleLoginUser(null)}>
                INGRESAR
              </Button>
            </footer>
          </section>
        </fieldset>
        <Box height={"20%"} sx={{ textAlign: "center" }} width={"100%"}>
          {isLoading && <CircularProgress color="success" />}
          {isError && <h1 className="text-lg font-bold text-red-700">Credenciales incorrectas</h1>}
        </Box>
      </form>
    </div>
  );
};
