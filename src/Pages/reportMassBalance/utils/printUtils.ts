import { TableData } from "../models/table.type";

/**
 * Generates CSS styles for print layout
 */
const getPrintStyles = (): string => {
  return `
    <style>
      @media print {
        .preview-notification-container > * {
          display: none;
        }
      }  
        
      
      @page {
         size: A4 landscape;
         margin: 0.5in;
      }
      
      .preview-notification-container {
        background: grey;
        display: block;
        width: 100%;
      }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          font-size: 12px;
          line-height: 1.4;
          color: #000;
          background: white;
        }
        
        .print-container {
          width: 100%;
          max-width: none;
        }
        
        .print-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: black;
          }
          
          .print-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-bottom: 20px;
            background: white;
            border: 1px solid #EBEBEB;
            border-radius: 8px;
            overflow: auto;
          }
          
          .print-table th,
          .print-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #EBEBEB;
            border-right: 1px solid #EBEBEB;
            font-size: 10px;
          }
    </style>
  `;
};

/**
 * Generates HTML content for the Mass Balance report
 */
export const generatePrintHTML = (data: TableData, reportTitle: string = "Balance de Masas"): string => {
  const styles = getPrintStyles();
  
  const massBalanceTableHTML = `
    <div class="no-break">
      <h3 class="section-title">F14 Toneladas Provenientes del Área de Prestación y F34 Disposición Final Operador</h3>
      <table class="print-table">
        <thead>
          <tr>
            <th rowspan="2" class="border-r">Área de Prestación</th>
            <th rowspan="2" class="border-r">Nombre de Área de Prestación</th>
            <th colspan="6">F14 Toneladas Provenientes del Área de Prestación</th>
            <th colspan="3">F34 Disposición Final Operador</th>
            <th rowspan="2" class="border-l">Balance</th>
          </tr>
          <tr>
            <th>Toneladas de Limpieza Urbana</th>
            <th>Toneladas de Barrido</th>
            <th>Toneladas de Residuos No Aprovechables</th>
            <th>Toneladas de Rechazos de Residuos Aprovechados</th>
            <th>Toneladas de Residuos Aprovechables</th>
            <th class="border-r">Toneladas de Barrido + Toneladas de Residuos No Aprovechables</th>
            <th>Total por NUAP</th>
            <th>Descuentos</th>
            <th>Total por NUAP - Descuentos</th>
            <th class="border-l">Diferencia (F34-F14)</th>
          </tr>
        </thead>
        <tbody>
          <tr>
              <td class="border-x">asd</td>
              <td class="border-r">asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td class="border-r">asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td class="border-l sticky-column">asd</td>
            </tr>
        </tbody>
        <tfoot>
          <tr>
            <td class="border-r">SUMA</td>
            <td style="font-weight: bold;">TOTAL</td>
            <td>asd</td>
            <td>asd</td>
            <td>asd</td>
            <td>asd</td>
            <td>asd</td>
            <td class="border-r">asd</td>
            <td>asd</td>
            <td>asd</td>
            <td>asd</td>
            <td class="border-l sticky-column">asd</td>
          </tr>
        </tfoot>
      </table>
    </div>
  `;

  const finalDispositionTableHTML = `
    <div class="no-break">
      <h3 class="section-title">Disposición Final</h3>
      <table class="print-table">
        <thead>
          <tr>
            <th colspan="4">Disposición Final</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th class="border-l">Total Disposición Final</th>
            <td>adsd</td>
            <th>Total Emvarias</th>
            <td class="border-r">asd</td>
          </tr>
        </tbody>
      </table>
    </div>
  `;

  const distributionTableHTML = `
    <div class="no-break page-break">
      <h3 class="section-title">Distribución</h3>
      <table class="print-table">
        <thead>
          <tr>
            <th colspan="7">Distribución</th>
          </tr>
          <tr>
            <th>Área de Prestación</th>
            <th>Toneladas Facturadas</th>
            <th>Nro Viajes</th>
            <th>Tonxviaje</th>
            <th>Toneladas Totales Rutas Compartidas</th>
            <th>% Distpeaje</th>
            <th>Toneladas a Compensar</th>
          </tr>
        </thead>
        <tbody>
          <tr>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
              <td>asd</td>
            </tr>
        </tbody>
      </table>
    </div>
  `;

  return `
    <!DOCTYPE html>
    <html lang="es">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${reportTitle}</title>
      ${styles}
    </head>
    <body>
    <div class="preview-notification-container">
      <h1 class='preview-notification'>Previsualización de PDF:</h1>
    </div>
    
      <div class="print-container">
        <h1 class="print-title">${reportTitle}</h1>
        ${massBalanceTableHTML}
        ${finalDispositionTableHTML}
        ${distributionTableHTML}
      </div>
    </body>
    </html>
  `;
};

/**
 * Opens a print popup window with the Mass Balance report
 */
export const openPrintWindow = (data: TableData, reportTitle?: string): void => {
  // Generate dynamic title with current date and time
  const now = new Date();
  const formattedDateTime = now.toLocaleString('es-CO', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });

  const dynamicTitle = `Balance de Masas - ${formattedDateTime}`;
  const printHTML = generatePrintHTML(data, reportTitle || dynamicTitle);

  const detectedMonitorWidth = window.outerWidth;
  const detectedMonitorHeight = window.outerHeight;

  const printWindow = window.open('', '_blank', `width=${detectedMonitorWidth},height=${detectedMonitorHeight},scrollbars=yes,resizable=yes`);

  if (!printWindow) {
    alert('Por favor, permite las ventanas emergentes para imprimir el reporte.');
    return;
  }

  // Set the window title to the dynamic title
  printWindow.document.write(printHTML);
  printWindow.document.close();
  printWindow.document.title = dynamicTitle;
  
  // Wait for content to load before triggering print
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
    }, 500);
  };
  
  // Fallback for browsers that don't support onload
  setTimeout(() => {
    if (printWindow && !printWindow.closed) {
      printWindow.focus();
      printWindow.print();
    }
  }, 1000);
};
