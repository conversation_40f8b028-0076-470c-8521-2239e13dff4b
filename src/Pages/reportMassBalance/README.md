# Mass Balance Report - Print to PDF Feature

## Overview

The Mass Balance report now includes a "Print to PDF" button that allows users to generate a print-friendly version of the complete report. This feature opens a new popup window with properly formatted content that can be printed directly or saved as PDF using the browser's native functionality.

## Features

### Conditional Display
The "Print to PDF" button is only visible when:
- The Mass Balance has been successfully filtered
- There is data available to display in the report
- The report has finished loading (`isFetched && isSuccess && !!parsedMassBalance`)

### Functionality
When clicked, the button:
1. Opens a new popup window with optimized print layout
2. Includes all Mass Balance tables:
   - F14 Toneladas Provenientes del Área de Prestación
   - F34 Disposición Final Operador
   - Disposición Final summary
   - Distribución table
3. Applies print-specific CSS styles for optimal formatting
4. Automatically triggers the browser's print dialog
5. Allows users to print directly or save as PDF

### User Experience
- Button is clearly labeled as "Imprimir PDF"
- Shows loading state ("Preparando...") while processing
- Positioned alongside other export buttons (Export Discounts, Generate SUI)
- Handles errors gracefully with user-friendly messages
- Respects popup blocker settings with appropriate messaging

## Technical Implementation

### Components

#### PrintToPDFButton
- **Location**: `src/Pages/reportMassBalance/components/PrintToPDFButton.tsx`
- **Props**:
  - `data: TableData | null` - The mass balance data to print
  - `disabled?: boolean` - Optional prop to disable the button
- **Features**:
  - Conditional rendering based on data availability
  - Loading state management
  - Error handling with user feedback

#### Print Utilities
- **Location**: `src/Pages/reportMassBalance/utils/printUtils.ts`
- **Functions**:
  - `generatePrintHTML(data: TableData, reportTitle?: string): string` - Generates complete HTML for printing
  - `openPrintWindow(data: TableData, reportTitle?: string): void` - Opens popup window and triggers print

### Styling

The print functionality includes comprehensive CSS styles optimized for:
- **Print Layout**: A4 landscape orientation with appropriate margins
- **Typography**: Clean, readable fonts with proper sizing
- **Tables**: Properly formatted borders, spacing, and colors
- **Colors**: Mercury brand colors (`#6e4299`) for headers and accents
- **Page Breaks**: Strategic placement to avoid breaking table content
- **Responsive Design**: Works on both screen preview and print output

### Integration

The button is integrated into the main Mass Balance component (`src/Pages/reportMassBalance/index.tsx`) within the `ReportFiltersDrawer` component's `extraButtons` section, positioned between the Export Discounts and Generate SUI buttons.

## Testing

### Unit Tests
- **Print Utils Tests**: `src/Pages/reportMassBalance/utils/printUtils.test.ts`
  - HTML generation validation
  - Window opening functionality
  - Error handling scenarios
  - Custom title support

- **Component Tests**: `src/Pages/reportMassBalance/components/PrintToPDFButton.test.tsx`
  - Button rendering with different states
  - Click event handling
  - Loading state management
  - Error handling
  - Accessibility features

### Running Tests
```bash
# Run all Mass Balance tests
npm test -- src/Pages/reportMassBalance --run

# Run specific test files
npm test -- src/Pages/reportMassBalance/utils/printUtils.test.ts --run
npm test -- src/Pages/reportMassBalance/components/PrintToPDFButton.test.tsx --run
```

## Browser Compatibility

The print functionality works with all modern browsers and includes:
- Popup blocker detection and user guidance
- Fallback mechanisms for different browser behaviors
- Cross-browser CSS compatibility
- Native print dialog integration

## Usage

1. Navigate to the Mass Balance report page
2. Apply filters to load report data
3. Once data is loaded, the "Imprimir PDF" button will appear
4. Click the button to open the print preview
5. Use the browser's print dialog to:
   - Print directly to a printer
   - Save as PDF
   - Adjust print settings as needed

## Future Enhancements

Potential improvements could include:
- Custom page headers/footers with report metadata
- Print preview within the application
- Additional export formats
- Print settings customization
- Batch printing for multiple periods
