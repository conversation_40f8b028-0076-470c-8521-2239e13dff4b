import { Grid, Typography } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { Tooltip } from "react-tooltip";
import dayjs, { Dayjs } from "dayjs";

import { NoResults } from "@/Orion.CORE.Frontend/components";
import { getLocalStorage } from "@/Orion.CORE.Frontend/helpers";
import { DataFilterT } from "@/Orion.CORE.Frontend/models";
import { useFiltersStore } from "@/Orion.CORE.Frontend/store";

import { nameModules } from "@/constants/nameModule";
import { Header, ReportFiltersDrawer } from "@/components";
import TableSkeleton from "@/components/TableLoadingSkeleton";
import MassBalanceStatusBadge from "@/Pages/reportMassBalance/components/MassBalanceStatusBadge.tsx";
import { useGetRecyclingArea } from "@/service/api/getRecyclingArea.ts";
import DistributionTableItem from "@/Pages/reportMassBalance/components/DistributionTableItem.tsx";
import MassBalanceTableItem from "@/Pages/reportMassBalance/components/MassBalanceTableItem.tsx";
import ExportDiscountButton from "@/Pages/reportMassBalance/components/ExportDiscountButton.tsx";
import PrintToPDFButton from "@/Pages/reportMassBalance/components/PrintToPDFButton.tsx";
import GenerateSUIButton from "@/components/GenerateSUIButton.tsx";

import FiltersMassBalance from "./components/FiltersMassBalance";
import { useUrlReportMassBalanceStore } from "./store/useUrlReportMassBalanceStore";
import { FilterMassBalanceE, nameModule } from "./helpers";
import { useReport } from "./hook/useReport";

const filterOpenInit: Record<string, DataFilterT> = {
  [FilterMassBalanceE.startDate]: {
    open: false,
    label: "Periodo",
  },
};

const ReportMassBalance = () => {
  const setInitStateDrawer = useFiltersStore((state) => state.setInitStateDrawer);
  const setStatePoppers = useFiltersStore((state) => state.setStatePoppers);
  const setIsFiltersApplied = useFiltersStore((state) => state.setIsFiltersApplied);
  const urlResult = useUrlReportMassBalanceStore((state) => state.url);

  const isApplied = getLocalStorage(nameModule) ?? true;

  const setMaxDate = useFiltersStore((state) => state.setMaxDate);
  const setSelectedDate = useFiltersStore((state) => state.setDateSelected);

  const [currentSelectedDate, setCurrentSelectedDate] = useState<Dayjs | undefined | null>(null);

  useEffect(() => {
    setInitStateDrawer(nameModules.FILTERMASSBALANCE, "right");
    setStatePoppers(filterOpenInit);
    setIsFiltersApplied(nameModules.FILTERMASSBALANCE, isApplied);
  }, [urlResult]);

  const { data: serviceArea } = useGetRecyclingArea();

  const { isSuccess, isFetching, isFetched, parsedMassBalance } = useReport(serviceArea);

  const tableRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollLeft = tableRef.current.scrollWidth;
    }
  }, [parsedMassBalance]);

  useEffect(() => {
    if (isMounted) return;

    setIsMounted(true);
  }, []);

  const clearPeriodFilter = () => {
    setMaxDate(FilterMassBalanceE.startDate, dayjs().startOf("month").add(-1, "month"));

    if (!isFetched && !isFetching) {
      setSelectedDate(
        nameModule,
        FilterMassBalanceE.startDate,
        dayjs().startOf("month").add(-1, "month"),
      );
      return;
    }

    setSelectedDate(nameModule, FilterMassBalanceE.startDate, currentSelectedDate || null);
  };

  const ReportHeader = () => {
    return (
      <div className="flex justify-between w-full items-center py-5">
        <Header title="Balance de Masas">
          <>
            {isFetched && isSuccess && (
              <MassBalanceStatusBadge isCorrect={parsedMassBalance?.isValid} />
            )}
            <ReportFiltersDrawer
              extraButtons={
                <>
                  {isFetched && isSuccess && <ExportDiscountButton />}
                  {isFetched && isSuccess && !!parsedMassBalance && (
                    <PrintToPDFButton data={parsedMassBalance} />
                  )}
                  {isFetched && isSuccess && (
                    <GenerateSUIButton isValid={parsedMassBalance?.isValid} />
                  )}
                </>
              }
              isCustomReport={true}
              isDownloadable={isFetched && isSuccess}
              moduleFiltersName={nameModule}
              moduleName={nameModules.FILTERMASSBALANCE}
              onClose={clearPeriodFilter}
            >
              <FiltersMassBalance
                clearPeriodFilter={clearPeriodFilter}
                isMounted={isMounted}
                setCurrentSelectedDate={setCurrentSelectedDate}
                title="Filtros"
              />
            </ReportFiltersDrawer>
          </>
        </Header>
      </div>
    );
  };

  return (
    <>
      <ReportHeader />
      <div className="container min-w-full px-5">
        {!isFetched && !isFetching && <NoResults />}
        {isFetched && parsedMassBalance === null && (
          <NoResults
            text={
              <Typography className="text-center" fontSize={16} marginTop={3}>
                No se han encontrado resultados para esta búsqueda <br />
                <Typography fontSize={16} fontWeight={600} variant="caption">
                  Intentá nuevamente seleccionando otro periodo
                </Typography>
              </Typography>
            }
          />
        )}
        {isFetching && <TableSkeleton />}
        {!isFetching && isFetched && isSuccess && !!parsedMassBalance && (
          <>
            <Grid
              container
              alignContent="flex-start"
              height={"auto"}
              justifyContent="center"
              maxHeight={"80%"}
            >
              <div ref={tableRef} className="overflow-x-scroll mb-10">
                <table className="max-w-full border border-mercury-border rounded-lg border-separate border-spacing-0 bg-white">
                  <thead>
                    <tr
                      className="
                        [&>*]:min-w-[190px] [&>*]:p-2 [&>*]:align-middle
                        [&>*]:text-center [&>*]:font-medium [&>*]:text-mercury-primary
                      [&>*]:bg-mercury-primaryLight
                        [&>*]:border-y first:[&>*]:border-l last:[&>*]:border-r
                      [&>*]:border-mercury-primary first:[&>*]:rounded-tl-lg last:[&>*]:rounded-tr-lg
                      [&>*]:border-t-mercury-primaryLight [&>*]:border-b-mercury-primary
                      first:[&>*]:border-l-mercury-primaryLight last:[&>*]:border-r-mercury-primaryLight
                      "
                    >
                      <th className="border-r" colSpan={8}>
                        F14 Toneladas Provenientes del Área de Prestación
                      </th>
                      <th colSpan={3}>F34 Disposición Final Operador</th>
                      <th className="sticky right-0 shadow-lg border-x">Balance</th>
                    </tr>
                    <tr
                      className="
                        [&>*]:min-w-[190px] [&>*]:p-2
                        [&>*]:text-start [&>*]:font-medium [&>*]:text-mercury-primary
                      [&>*]:bg-mercury-primaryLight
                      [&>*]:border-mercury-primary [&>*]:border-b
                      first:[&>*]:border-l-mercury-primaryLight last:[&>*]:border-r-mercury-primaryLight
                       "
                    >
                      {/* F14 */}
                      <th className="border-x">Área de Prestación</th>
                      <th className="border-r w-[200px]">Nombre de Área de Prestación</th>
                      <th>
                        <a data-tooltip-id="f1">Toneladas de Limpieza Urbana</a>
                        <Tooltip
                          content="C8_TON_LIMP_URB(F14)"
                          id="f1"
                          place="bottom"
                          variant="light"
                        />
                      </th>
                      <th>
                        <a data-tooltip-id="f2">Toneladas de Barrido</a>
                        <Tooltip
                          content="C9_TON_BARRIDO(F14)"
                          id="f2"
                          place="bottom"
                          variant="light"
                        />
                      </th>
                      <th>
                        <a data-tooltip-id="f3">Toneladas de Residuos No Aprovechables</a>
                        <Tooltip
                          content="C10_TONRESNA(F14)"
                          id="f3"
                          place="bottom"
                          variant="light"
                        />
                      </th>
                      <th>
                        <a data-tooltip-id="f4">Toneladas de Rechazos de Residuos Aprovechados</a>
                        <Tooltip
                          content="C11_TONRECHAPR(F14)"
                          id="f4"
                          place="bottom"
                          variant="light"
                        />
                      </th>
                      <th>
                        <a data-tooltip-id="f5">Toneladas de Residuos Aprovechables</a>
                        <Tooltip
                          content="C12_TONRESAPR(F14)"
                          id="f5"
                          place="bottom"
                          variant="light"
                        />
                      </th>
                      <th className="border-r">
                        <a data-tooltip-id="f6">
                          Toneladas de Barrido + Toneladas de Residuos No Aprovechables
                        </a>
                        <Tooltip content="C9+C10(F14)" id="f6" place="bottom" variant="light" />
                      </th>
                      {/* F34 */}
                      <th>Total por NUAP</th>
                      <th>Descuentos</th>
                      <th>Total por NUAP - Descuentos</th>
                      {/* Balance */}
                      <th className="sticky right-0 border-x shadow-lg">Diferencia (F34-F14)</th>
                    </tr>
                  </thead>
                  <tbody>
                    {parsedMassBalance?.massBalance.map((item, index) => (
                      <MassBalanceTableItem
                        key={`massBalance-${index}`}
                        index={index}
                        item={item}
                      />
                    ))}
                  </tbody>
                  <tfoot>
                    <tr
                      className="
                        [&>*]:min-w-[190px] [&>*]:font-medium [&>*]:text-mercury-primary [&>*]:p-2
                      [&>*]:bg-mercury-primaryLight
                      [&>*]:border-t-mercury-primary [&>*]:border-t
                      [&*>]:border-mercury-primary [&>*]:border-x-mercury-primary
                        first:[&>*]:rounded-bl-lg last:[&>*]:rounded-br-lg
                      "
                    >
                      <td className="border-r">SUMA</td>
                      <td className="!font-semibold">TOTAL</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allUrbanCleaningTons}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allSweepingTons}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allNonRecyclableTons}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allRejectionTons}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allRecyclableTons}</td>
                      <td className="border-r">
                        {parsedMassBalance?.massBalanceTotals.totalTonsF14}
                      </td>
                      <td>{parsedMassBalance?.massBalanceTotals.totalTonsF34}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allDiscountTons}</td>
                      <td>{parsedMassBalance?.massBalanceTotals.allTotalsMinusDiscounts}</td>
                      <td className="sticky right-0 border-l shadow-lg">
                        {parsedMassBalance?.massBalanceTotals.totalDifference}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              <div className="mb-10 min-w-full">
                <table className="min-w-full border border-mercury-border rounded-lg border-separate border-spacing-0 bg-white">
                  <thead>
                    <tr
                      className="
                        [&>*]:min-w-[190px] [&>*]:p-2
                        [&>*]:align-middle [&>*]:text-center [&>*]:font-medium [&>*]:text-mercury-primary
                      [&>*]:bg-mercury-primaryLight
                        [&>*]:border-y first:[&>*]:border-l last:[&>*]:border-r
                      [&>*]:border-b-mercury-primary [&>*]:border-t-mercury-primaryLight
                      [&>*]:border-x-mercury-primaryLight
                        first:[&>*]:rounded-tl-lg last:[&>*]:rounded-tr-lg
                      "
                    >
                      <th className="border-r" colSpan={8}>
                        Disposición Final
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      className="
                        [&>*]:min-w-[190px] [&>*]:p-2 [&>*]:text-mercury-primary
                      even:[&>*]:bg-neutral-100
                      [&>*]:border-mercury-primaryLight [&>*]:border-b
                        first:[&>*]:rounded-bl-lg last:[&>*]:rounded-br-lg
                      "
                    >
                      <th className="border-l">Total Disposición Final</th>
                      <td>{parsedMassBalance.finalDispositionTotals.total}</td>
                      <th>Total Emvarias</th>
                      <td className="border-r">
                        {parsedMassBalance.finalDispositionTotals.emvariasTotal}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <table className="min-w-full border border-mercury-border rounded-lg border-separate border-spacing-0 bg-white mb-10">
                <thead>
                  <tr
                    className="
                      [&>*]:min-w-[190px] [&>*]:p-2 [&>*]:align-middle
                      [&>*]:text-center [&>*]:font-medium [&>*]:text-mercury-primary
                    [&>*]:bg-mercury-primaryLight
                      [&>*]:border-y first:[&>*]:border-l last:[&>*]:border-r
                      first:[&>*]:rounded-tl-lg last:[&>*]:rounded-tr-lg
                    [&>*]:border-t-mercury-primaryLight [&>*]:border-b-mercury-primary
                    first:[&>*]:border-l-mercury-primaryLight last:[&>*]:border-r-mercury-primaryLight
                    "
                  >
                    <th className="border-r" colSpan={7}>
                      Distribución
                    </th>
                  </tr>
                  <tr
                    className="
                          [&>*]:p-2 [&>*]:min-w-[190px] [&>*]:text-start [&>*]:font-medium [&>*]:text-mercury-primary
                          [&>*]:bg-mercury-primaryLight
                          [&>*]:border-b [&>*]:border-b-mercury-primary
                          first:[&>*]:border-l-mercury-primaryLight last:[&>*]:border-r-mercury-primaryLight
                        "
                  >
                    <th>Área de Prestación</th>
                    <th>Toneladas Facturadas</th>
                    <th>Nro Viajes</th>
                    <th>Tonxviaje</th>
                    <th>Toneladas Totales Rutas Compartidas</th>
                    <th>% Distpeaje</th>
                    <th>Toneladas a Compensar</th>
                  </tr>
                </thead>
                <tbody className="first:[&>*]:rounded-bl-lg last:[&>*]:rounded-br-lg">
                  {parsedMassBalance?.distributions.map((item, index) => (
                    <DistributionTableItem key={`distribution-${index}`} item={item} />
                  ))}
                </tbody>
              </table>
            </Grid>
          </>
        )}
      </div>
    </>
  );
};

export default ReportMassBalance;
