import { useState } from "react";
import { Button, Typography } from "@mui/material";
import PrintIcon from "@mui/icons-material/Print";

import { TableData } from "../models/table.type";
import { openPrintWindow } from "../utils/printUtils";

interface PrintToPDFButtonProps {
  data: TableData | null;
  disabled?: boolean;
}

const PrintToPDFButton = ({ data, disabled = false }: PrintToPDFButtonProps) => {
  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = () => {
    if (!data) return;

    setIsPrinting(true);

    try {
      openPrintWindow(data, "Balance de Masas");
    } catch (error) {
      console.error("Error al abrir la ventana de impresión:", error);
      alert("Error al abrir la ventana de impresión. Por favor, inténtalo de nuevo.");
    } finally {
      // Reset the printing state after a short delay
      setTimeout(() => {
        setIsPrinting(false);
      }, 2000);
    }
  };

  const isDisabled = disabled || !data || isPrinting;

  return (
    <div>
      <Button
        id="print-pdf-button"
        disabled={isDisabled}
        startIcon={
          <PrintIcon 
            className={!isPrinting ? "" : "animate-pulse text-[#6e4299]"} 
          />
        }
        onClick={handlePrint}
      >
        <Typography 
          className="text-lg font-bold" 
          color={isDisabled ? "textSecondary" : "primary"}
        >
          {isPrinting ? "Preparando..." : "Imprimir PDF"}
        </Typography>
      </Button>
    </div>
  );
};

export default PrintToPDFButton;
