import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import PrintToPDFButton from './PrintToPDFButton';
import { TableData } from '../models/table.type';
import * as printUtils from '../utils/printUtils';

// Mock the print utils
vi.mock('../utils/printUtils', () => ({
  openPrintWindow: vi.fn()
}));

const mockTableData: TableData = {
  massBalance: [
    {
      nuap: "Test NUAP",
      serviceArea: "Test Area",
      urbanCleaningTons: "100.00",
      sweepingTons: "50.00",
      nonRecyclableTons: "75.00",
      rejectionTons: "25.00",
      recyclableTons: "30.00",
      totalTons: "280.00",
      totalByNuap: "300.00",
      discountTons: "20.00",
      totalByNuapDiscounts: "280.00",
      difference: "0.00"
    }
  ],
  massBalanceTotals: {
    allUrbanCleaningTons: "100.00",
    allSweepingTons: "50.00",
    allNonRecyclableTons: "75.00",
    allRejectionTons: "25.00",
    allRecyclableTons: "30.00",
    totalTonsF14: "280.00",
    allDiscountTons: "20.00",
    allTotalsMinusDiscounts: "280.00",
    totalTonsF34: "300.00",
    totalDifference: "0.00"
  },
  finalDispositionTotals: {
    total: "300.00",
    emvariasTotal: "280.00"
  },
  distributions: [
    {
      serviceArea: "Test Distribution Area",
      tons: "100.00",
      trips: "10",
      tonPerTrip: "10.00",
      tollSharedRouteTons: "50.00",
      tollPercentage: "50",
      compensationTons: "5.00"
    }
  ],
  isValid: true
};

describe('PrintToPDFButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the button with correct text when data is available', () => {
    render(<PrintToPDFButton data={mockTableData} />);
    
    expect(screen.getByText('Imprimir PDF')).toBeInTheDocument();
    expect(screen.getByRole('button')).not.toBeDisabled();
  });

  it('should render disabled button when no data is provided', () => {
    render(<PrintToPDFButton data={null} />);
    
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('should render disabled button when explicitly disabled', () => {
    render(<PrintToPDFButton data={mockTableData} disabled={true} />);
    
    expect(screen.getByRole('button')).toBeDisabled();
  });

  it('should call openPrintWindow when clicked with valid data', () => {
    const openPrintWindowSpy = vi.spyOn(printUtils, 'openPrintWindow');
    
    render(<PrintToPDFButton data={mockTableData} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(openPrintWindowSpy).toHaveBeenCalledWith(mockTableData, "Balance de Masas");
  });

  it('should not call openPrintWindow when clicked without data', () => {
    const openPrintWindowSpy = vi.spyOn(printUtils, 'openPrintWindow');
    
    render(<PrintToPDFButton data={null} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(openPrintWindowSpy).not.toHaveBeenCalled();
  });

  it('should show "Preparando..." text while printing', async () => {
    vi.useFakeTimers();

    render(<PrintToPDFButton data={mockTableData} />);

    const button = screen.getByRole('button');

    act(() => {
      fireEvent.click(button);
    });

    expect(screen.getByText('Preparando...')).toBeInTheDocument();

    // Fast-forward time to reset the printing state
    act(() => {
      vi.advanceTimersByTime(2000);
    });

    expect(screen.getByText('Imprimir PDF')).toBeInTheDocument();

    vi.useRealTimers();
  });

  it('should handle print errors gracefully', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    
    vi.spyOn(printUtils, 'openPrintWindow').mockImplementation(() => {
      throw new Error('Print error');
    });
    
    render(<PrintToPDFButton data={mockTableData} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(consoleSpy).toHaveBeenCalledWith('Error al abrir la ventana de impresión:', expect.any(Error));
    expect(alertSpy).toHaveBeenCalledWith('Error al abrir la ventana de impresión. Por favor, inténtalo de nuevo.');
    
    consoleSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('should have correct button id for testing purposes', () => {
    render(<PrintToPDFButton data={mockTableData} />);
    
    expect(screen.getByRole('button')).toHaveAttribute('id', 'print-pdf-button');
  });
});
