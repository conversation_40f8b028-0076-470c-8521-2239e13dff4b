/// <reference types="vitest" />

import * as path from "path";

import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [react()],
  build: {
    target: "esnext",
    chunkSizeWarningLimit: 650,
  },
  resolve: {
    alias: {
      "@/nonCompliance": path.resolve(__dirname, "./apps/reports/nonCompliance"),
      "@": path.resolve(__dirname, "./src"),
    },
  },
  test: {
    globals: true,
    environment: "jsdom",
    include: ["./src/**/*.test.(ts|tsx)", "./apps/reports/**/*.test.(ts|tsx)"],
    setupFiles: "./tests/setup.ts",
    coverage: {
      provider: "istanbul",
      reporter: ["html"],
      enabled: true,
    },
  },
});
