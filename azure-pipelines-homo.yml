# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
- homo

pool:
  vmImage: 'windows-latest'

steps:
- script: |
    npm install
  displayName: 'Instalar npm'

# Clonamos Orion CORE 
- task: DownloadPipelineArtifact@2
  inputs:
    buildType: 'specific'
    project: '849eeb8f-bf80-4dcb-89a5-2666a990a470'
    definition: '145'
    buildVersionToDownload: 'latest'
    artifactName: 'Orion.CORE.Frontend'
    targetPath: '$(Pipeline.Workspace)/Orion.CORE.Frontend'
  displayName: 'Clone de Orion.CORE'

# Movemos Orion CORE a la carpeta de Namek

- task: CmdLine@2
  inputs:
    script: |
      cd $(Pipeline.Workspace)
      move Orion.CORE.Frontend $(System.DefaultWorkingDirectory)/src
  displayName: 'Move Orion.CORE.Frontend to src'

# Instalar Node.js
- task: NodeTool@0
  inputs:
    versionSpec: '18.x'
  displayName: 'Install Node.js'

# Instalar dependencias del paquete
- task: CmdLine@2
  inputs:
    script: 'npm i'
  displayName: 'Instalar packages'
  
- task: CmdLine@2
  inputs:
    script: |
      cd $(System.DefaultWorkingDirectory)
      npm run buildHomo
      dir $(System.DefaultWorkingDirectory)
  displayName: 'Ejecutamos el npm run build'

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(System.DefaultWorkingDirectory)/dist'
    ArtifactName: 'MercuryFrontend'
    publishLocation: 'Container'